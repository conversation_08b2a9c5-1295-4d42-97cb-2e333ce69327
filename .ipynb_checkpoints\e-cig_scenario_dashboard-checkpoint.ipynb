{"cells": [{"cell_type": "markdown", "id": "fe112dde", "metadata": {}, "source": ["\n", "# Dashboard di Scenario per il Mercato E-Cig Sud Europa\n", "\n", "Questo notebook interattivo permette di simulare diversi scenari di crescita per il mercato delle sigarette elettroniche e dei prodotti a tabacco riscaldato in Italia, Spagna, Portogallo e Grecia. Utilizza **ipywidgets** per modificare i parametri e **Plotly** per visualizzare i risultati.\n", "\n", "## Come usare il simulatore\n", "\n", "1. <PERSON><PERSON> i tassi di crescita annuale per ogni paese tramite gli slider.\n", "2. Imposta la distribuzione delle categorie (sistemi chiusi, sistemi aperti e dispositivi monouso). I valori devono sommare al 100%.\n", "3. <PERSON><PERSON><PERSON> l'orizzonte temporale (numero di anni da simulare).\n", "4. Fai clic su **Aggiorna** per generare il grafico interattivo con i nuovi parametri.\n", "5. Usa **Reset** per tornare ai valori predefiniti.\n", "\n", "I dati di base utilizzati provengono da ricerche di mercato recenti e sono riportati nel report principale.\n", "\n", "## <PERSON><PERSON> migliorata\n", "- ✅ Validazione input completa\n", "- ✅ Gestione errori migliorata\n", "- ✅ Calcoli corretti per tutte le categorie\n", "- ✅ Interfaccia utente ottimizzata\n", "- ✅ Funzione reset\n"]}, {"cell_type": "code", "execution_count": null, "id": "6589cea7", "metadata": {}, "outputs": [], "source": ["# Importazioni con gestione errori\n", "try:\n", "    import pandas as pd\n", "    import numpy as np\n", "    import plotly.graph_objects as go\n", "    import plotly.express as px\n", "    import ipywidgets as widgets\n", "    from IPython.display import display, HTML\n", "    import warnings\n", "    warnings.filterwarnings('ignore')\n", "    print(\"✅ Tutte le librerie caricate correttamente\")\n", "except ImportError as e:\n", "    print(f\"❌ Errore nell'importazione: {e}\")\n", "    print(\"Instal<PERSON> le dipendenze con: pip install pandas numpy plotly ipywidgets\")\n", "\n", "# Dati base (milioni USD) per il 2024\n", "base_revenue = {\n", "    'Italia': 726.33,   # mercato vape 2024 (Cognitive Market Research)\n", "    'Spagna': 692.54,   # mercato vape 2024 (Cognitive Market Research)\n", "    'Portogallo': 177.36, # mercato vape 2024 (Cognitive Market Research)\n", "    'Grecia': 101.35    # mercato vape 2024 (Cognitive Market Research)\n", "}\n", "\n", "# Dati base HnB (Heat-not-burn) 2024 (solo per Italia – altri paesi dati non disponibili)\n", "base_hnb = {\n", "    'Italia': 4500,  # milioni USD (TobaccoIntelligence 2024)\n", "    'Spagna': 0,\n", "    'Portogallo': 0,\n", "    'Grecia': 0\n", "}\n", "\n", "# Valori predefiniti per reset\n", "DEFAULT_VALUES = {\n", "    'growth_rates': {country: 10.0 for country in base_revenue.keys()},\n", "    'share_closed': 70.0,\n", "    'share_disposable': 10.0,\n", "    'years': 5,\n", "    'include_hnb': False\n", "}\n", "\n", "def validate_inputs(growth_rates, share_closed, share_disposable, years):\n", "    \"\"\"Valida tutti gli input e restituisce errori se presenti\"\"\"\n", "    errors = []\n", "    \n", "    # Controlla tassi di crescita\n", "    for country, rate in growth_rates.items():\n", "        if rate < -50 or rate > 100:\n", "            errors.append(f\"Tasso di crescita per {country} fuori range (-50%, +100%): {rate}%\")\n", "    \n", "    # Controlla percentuali\n", "    if share_closed < 0 or share_closed > 100:\n", "        errors.append(f\"Percentuale sistemi chiusi fuori range (0-100%): {share_closed}%\")\n", "    \n", "    if share_disposable < 0 or share_disposable > 100:\n", "        errors.append(f\"Percentuale monouso fuori range (0-100%): {share_disposable}%\")\n", "    \n", "    if share_closed + share_disposable > 100:\n", "        errors.append(f\"Somma percentuali > 100%: <PERSON><PERSON><PERSON>({share_closed}%) + <PERSON><PERSON><PERSON>({share_disposable}%) = {share_closed + share_disposable}%\")\n", "    \n", "    # <PERSON>la anni\n", "    if years < 1 or years > 20:\n", "        errors.append(f\"Orizzonte temporale fuori range (1-20 anni): {years}\")\n", "    \n", "    return errors\n", "\n", "def compute_forecast(growth_rates, share_closed, share_disposable, years, include_hnb=False):\n", "    \"\"\"Calcola le proiezioni con logica corretta\"\"\"\n", "    # Validazione input\n", "    errors = validate_inputs(growth_rates, share_closed, share_disposable, years)\n", "    if errors:\n", "        raise ValueError(\"\\n\".join(errors))\n", "    \n", "    share_open = 100 - share_closed - share_disposable\n", "    forecast_data = []\n", "    \n", "    for country, base in base_revenue.items():\n", "        vape_revenue = base\n", "        hnb_revenue = base_hnb.get(country, 0)\n", "        growth_rate = growth_rates.get(country, 0) / 100\n", "        \n", "        for year in range(years + 1):\n", "            # Calcola ricavi totali\n", "            total_hnb = hnb_revenue if include_hnb else 0\n", "            total_revenue = vape_revenue + total_hnb\n", "            \n", "            # Aggiungi dati al dataframe\n", "            forecast_data.append({\n", "                'Anno': 2024 + year,\n", "                'Paese': country,\n", "                'Categoria': 'Totale',\n", "                'Ricavi': total_revenue\n", "            })\n", "            \n", "            # Ricavi per categoria (basati sui ricavi vape correnti)\n", "            forecast_data.extend([\n", "                {\n", "                    'Anno': 2024 + year,\n", "                    'Paese': country,\n", "                    'Categoria': '<PERSON><PERSON><PERSON>',\n", "                    'Ricavi': vape_revenue * (share_closed/100)\n", "                },\n", "                {\n", "                    'Anno': 2024 + year,\n", "                    'Paese': country,\n", "                    'Categoria': 'Aperti',\n", "                    'Ricavi': vape_revenue * (share_open/100)\n", "                },\n", "                {\n", "                    'Anno': 2024 + year,\n", "                    'Paese': country,\n", "                    'Categoria': '<PERSON><PERSON><PERSON>',\n", "                    'Ricavi': vape_revenue * (share_disposable/100)\n", "                }\n", "            ])\n", "            \n", "            if include_hnb and hnb_revenue > 0:\n", "                forecast_data.append({\n", "                    'Anno': 2024 + year,\n", "                    'Paese': country,\n", "                    'Categoria': 'HnB',\n", "                    'Ricavi': hnb_revenue\n", "                })\n", "            \n", "            # Applica crescita per l'anno successivo\n", "            vape_revenue *= (1 + growth_rate)\n", "            hnb_revenue *= (1 + growth_rate)\n", "    \n", "    return pd.DataFrame(forecast_data)\n"]}, {"cell_type": "code", "execution_count": null, "id": "13a4e7fe", "metadata": {}, "outputs": [], "source": ["# Creazione degli slider per i tassi di crescita con validazione migliorata\n", "growth_sliders = {}\n", "for country in base_revenue.keys():\n", "    growth_sliders[country] = widgets.FloatSlider(\n", "        value=DEFAULT_VALUES['growth_rates'][country],\n", "        min=-50.0,\n", "        max=100.0,\n", "        step=0.5,\n", "        description=f\"Crescita {country} (%)\",\n", "        continuous_update=False,\n", "        style={'description_width': 'initial'}\n", "    )\n", "\n", "# Slider per la percentuale di dispositivi monouso\n", "disposable_slider = widgets.FloatSlider(\n", "    value=DEFAULT_VALUES['share_disposable'],\n", "    min=0.0,\n", "    max=100.0,\n", "    step=1.0,\n", "    description='<PERSON><PERSON><PERSON> (%)',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Slider per la percentuale di sistemi chiusi\n", "closed_slider = widgets.FloatSlider(\n", "    value=DEFAULT_VALUES['share_closed'],\n", "    min=0.0,\n", "    max=100.0,\n", "    step=1.0,\n", "    description='<PERSON><PERSON><PERSON> (%)',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Label per sistemi aperti (calcolato automaticamente)\n", "open_label = widgets.Label(value=\"Aperti (%): 20.0\")\n", "\n", "# Slider per la durata dell'orizzonte temporale\n", "years_slider = widgets.IntSlider(\n", "    value=DEFAULT_VALUES['years'],\n", "    min=1,\n", "    max=20,\n", "    step=1,\n", "    description='Orizzonte (anni)',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Checkbox per includere la categoria Heat-not-burn (solo Italia)\n", "hnb_checkbox = widgets.Checkbox(\n", "    value=DEFAULT_VALUES['include_hnb'],\n", "    description='Includi HnB (Italia)',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# <PERSON><PERSON><PERSON>\n", "update_button = widgets.Button(description='🔄 Aggiorna', button_style='info')\n", "reset_button = widgets.Button(description='🔄 Reset', button_style='warning')\n", "\n", "# Area di output per messaggi di errore\n", "error_output = widgets.Output()\n", "# Area di output per grafici\n", "plot_output = widgets.Output()\n", "\n", "def update_open_percentage():\n", "    \"\"\"Aggiorna automaticamente la percentuale di sistemi aperti\"\"\"\n", "    open_pct = max(0, 100 - closed_slider.value - disposable_slider.value)\n", "    open_label.value = f\"Aperti (%): {open_pct:.1f}\"\n", "    \n", "    # Cambia colore se la somma supera 100%\n", "    if closed_slider.value + disposable_slider.value > 100:\n", "        open_label.value = f\"⚠️ Aperti (%): {open_pct:.1f} (ERRORE: somma > 100%)\"\n", "\n", "# Collega l'aggiornamento automatico agli slider\n", "closed_slider.observe(lambda change: update_open_percentage(), names='value')\n", "disposable_slider.observe(lambda change: update_open_percentage(), names='value')\n", "\n", "def reset_values(button=None):\n", "    \"\"\"Reset tutti i valori ai default\"\"\"\n", "    for country, slider in growth_sliders.items():\n", "        slider.value = DEFAULT_VALUES['growth_rates'][country]\n", "    closed_slider.value = DEFAULT_VALUES['share_closed']\n", "    disposable_slider.value = DEFAULT_VALUES['share_disposable']\n", "    years_slider.value = DEFAULT_VALUES['years']\n", "    hnb_checkbox.value = DEFAULT_VALUES['include_hnb']\n", "    \n", "    with error_output:\n", "        error_output.clear_output()\n", "        print(\"✅ Valori resettati ai default\")\n", "\n", "def create_enhanced_plots(df, years, include_hnb):\n", "    \"\"\"Crea grafici migliorati con più visualizzazioni\"\"\"\n", "    # Grafico 1: <PERSON><PERSON> totali per paese\n", "    fig1 = go.Figure()\n", "    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']\n", "    \n", "    for i, country in enumerate(base_revenue.keys()):\n", "        df_country = df[(df['Paese'] == country) & (df['Categoria'] == 'Totale')]\n", "        fig1.add_trace(go.<PERSON>(\n", "            x=df_country['Anno'], \n", "            y=df_country['Ricavi'], \n", "            mode='lines+markers',\n", "            name=country,\n", "            line=dict(color=colors[i], width=3),\n", "            marker=dict(size=8)\n", "        ))\n", "    \n", "    fig1.update_layout(\n", "        title=\"📈 Proiezioni Ricavi Totali per Paese\",\n", "        xaxis_title=\"Anno\",\n", "        yaxis_title=\"<PERSON>vi (milioni USD)\",\n", "        legend_title=\"Paese\",\n", "        template='plotly_white',\n", "        height=500,\n", "        hovermode='x unified'\n", "    )\n", "    \n", "    # Grafico 2: Breakdown per categoria (ultimo anno)\n", "    last_year = 2024 + years\n", "    df_last = df[df['Anno'] == last_year]\n", "    \n", "    fig2 = go.Figure()\n", "    categories = ['Chiusi', 'Aperti', 'Monouso']\n", "    if include_hnb:\n", "        categories.append('HnB')\n", "    \n", "    for category in categories:\n", "        df_cat = df_last[df_last['Categoria'] == category]\n", "        if not df_cat.empty:\n", "            fig2.add_trace(go.Bar(\n", "                x=df_cat['Paese'],\n", "                y=df_cat['<PERSON><PERSON>'],\n", "                name=category\n", "            ))\n", "    \n", "    fig2.update_layout(\n", "        title=f\"📊 Breakdown per Categoria - Anno {last_year}\",\n", "        xaxis_title=\"Paese\",\n", "        yaxis_title=\"<PERSON>vi (milioni USD)\",\n", "        template='plotly_white',\n", "        height=400,\n", "        barmode='stack'\n", "    )\n", "    \n", "    return fig1, fig2\n", "\n", "def update_plot(button=None):\n", "    \"\"\"Funzione principale per aggiornare i grafici\"\"\"\n", "    with error_output:\n", "        error_output.clear_output()\n", "        \n", "    with plot_output:\n", "        plot_output.clear_output()\n", "        \n", "        try:\n", "            # Ra<PERSON><PERSON> i valori\n", "            growth_rates = {country: slider.value for country, slider in growth_sliders.items()}\n", "            share_closed = closed_slider.value\n", "            share_disposable = disposable_slider.value\n", "            years = years_slider.value\n", "            include_hnb = hnb_checkbox.value\n", "            \n", "            # Calcola le proiezioni\n", "            df = compute_forecast(growth_rates, share_closed, share_disposable, years, include_hnb)\n", "            \n", "            # Crea e mostra i grafici\n", "            fig1, fig2 = create_enhanced_plots(df, years, include_hnb)\n", "            fig1.show()\n", "            fig2.show()\n", "            \n", "            # <PERSON><PERSON> riassuntiva migliorata\n", "            first_year = 2024\n", "            last_year = 2024 + years\n", "            summary = df[df['Anno'].isin([first_year, last_year])].pivot_table(\n", "                index=['Paese', 'Categoria'], \n", "                columns='Anno', \n", "                values='Ricavi'\n", "            ).round(2)\n", "            \n", "            # Calcola crescita percentuale\n", "            if last_year in summary.columns and first_year in summary.columns:\n", "                summary['Crescita (%)'] = ((summary[last_year] / summary[first_year] - 1) * 100).round(1)\n", "            \n", "            print(\"\\n📋 <PERSON>bella <PERSON>:\")\n", "            display(summary)\n", "            \n", "            with error_output:\n", "                print(\"✅ Grafici aggiornati con successo!\")\n", "                \n", "        except ValueError as e:\n", "            with error_output:\n", "                print(f\"❌ Errore di validazione:\\n{str(e)}\")\n", "        except Exception as e:\n", "            with error_output:\n", "                print(f\"❌ Errore imprevisto: {str(e)}\")\n", "\n", "# Collega le funzioni ai bottoni\n", "update_button.on_click(update_plot)\n", "reset_button.on_click(reset_values)\n", "\n", "# Aggiorna inizialmente la percentuale aperti\n", "update_open_percentage()\n", "\n", "# Layout migliorato dell'interfaccia\n", "ui = widgets.VBox([\n", "    widgets.HTML(\"<h3>🎛️ Parametri di Simulazione</h3>\"),\n", "    \n", "    widgets.HTML(\"<h4>📈 Tassi di Crescita Annuale</h4>\"),\n", "    widgets.HBox(list(growth_sliders.values())),\n", "    \n", "    widgets.HTML(\"<h4>📊 Distribuzione Categorie</h4>\"),\n", "    widgets.HBox([closed_slider, disposable_slider]),\n", "    open_label,\n", "    \n", "    widgets.HTML(\"<h4>⚙️ Altre Opzioni</h4>\"),\n", "    widgets.HBox([years_slider, hnb_checkbox]),\n", "    \n", "    widgets.HTML(\"<h4>🎮 Controlli</h4>\"),\n", "    widgets.HBox([update_button, reset_button]),\n", "    \n", "    error_output,\n", "    plot_output\n", "])\n", "\n", "display(ui)\n", "\n", "# Messaggio di benvenuto\n", "print(\"🚀 Dashboard E-Cig Sud Europa caricata con successo!\")\n", "print(\"📝 Modifica i parametri e clicca 'Aggiorna' per vedere le proiezioni.\")\n"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}