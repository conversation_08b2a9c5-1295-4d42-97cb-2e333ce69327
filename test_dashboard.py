#!/usr/bin/env python3
"""
Test script per verificare la funzionalità della dashboard E-Cig
"""

import pandas as pd
import numpy as np

# Dati base (milioni USD) per il 2024
base_revenue = {
    'Italia': 726.33,   # mercato vape 2024 (Cognitive Market Research)
    'Spagna': 692.54,   # mercato vape 2024 (Cognitive Market Research)
    'Portogallo': 177.36, # mercato vape 2024 (Cognitive Market Research)
    'Grecia': 101.35    # mercato vape 2024 (Cognitive Market Research)
}

# Dati base HnB (Heat-not-burn) 2024 (solo per Italia – altri paesi dati non disponibili)
base_hnb = {
    'Italia': 4500,  # milioni USD (TobaccoIntelligence 2024)
    'Spagna': 0,
    'Portogallo': 0,
    'Grecia': 0
}

def validate_inputs(growth_rates, share_closed, share_disposable, years):
    """Valida tutti gli input e restituisce errori se presenti"""
    errors = []
    
    # Controlla tassi di crescita
    for country, rate in growth_rates.items():
        if rate < -50 or rate > 100:
            errors.append(f"Tasso di crescita per {country} fuori range (-50%, +100%): {rate}%")
    
    # Controlla percentuali
    if share_closed < 0 or share_closed > 100:
        errors.append(f"Percentuale sistemi chiusi fuori range (0-100%): {share_closed}%")
    
    if share_disposable < 0 or share_disposable > 100:
        errors.append(f"Percentuale monouso fuori range (0-100%): {share_disposable}%")
    
    if share_closed + share_disposable > 100:
        errors.append(f"Somma percentuali > 100%: Chiusi({share_closed}%) + Monouso({share_disposable}%) = {share_closed + share_disposable}%")
    
    # Controlla anni
    if years < 1 or years > 20:
        errors.append(f"Orizzonte temporale fuori range (1-20 anni): {years}")
    
    return errors

def compute_forecast(growth_rates, share_closed, share_disposable, years, include_hnb=False):
    """Calcola le proiezioni con logica corretta"""
    # Validazione input
    errors = validate_inputs(growth_rates, share_closed, share_disposable, years)
    if errors:
        raise ValueError("\n".join(errors))
    
    share_open = 100 - share_closed - share_disposable
    forecast_data = []
    
    for country, base in base_revenue.items():
        vape_revenue = base
        hnb_revenue = base_hnb.get(country, 0)
        growth_rate = growth_rates.get(country, 0) / 100
        
        for year in range(years + 1):
            # Calcola ricavi totali
            total_hnb = hnb_revenue if include_hnb else 0
            total_revenue = vape_revenue + total_hnb
            
            # Aggiungi dati al dataframe
            forecast_data.append({
                'Anno': 2024 + year,
                'Paese': country,
                'Categoria': 'Totale',
                'Ricavi': total_revenue
            })
            
            # Ricavi per categoria (basati sui ricavi vape correnti)
            forecast_data.extend([
                {
                    'Anno': 2024 + year,
                    'Paese': country,
                    'Categoria': 'Chiusi',
                    'Ricavi': vape_revenue * (share_closed/100)
                },
                {
                    'Anno': 2024 + year,
                    'Paese': country,
                    'Categoria': 'Aperti',
                    'Ricavi': vape_revenue * (share_open/100)
                },
                {
                    'Anno': 2024 + year,
                    'Paese': country,
                    'Categoria': 'Monouso',
                    'Ricavi': vape_revenue * (share_disposable/100)
                }
            ])
            
            if include_hnb and hnb_revenue > 0:
                forecast_data.append({
                    'Anno': 2024 + year,
                    'Paese': country,
                    'Categoria': 'HnB',
                    'Ricavi': hnb_revenue
                })
            
            # Applica crescita per l'anno successivo
            vape_revenue *= (1 + growth_rate)
            hnb_revenue *= (1 + growth_rate)
    
    return pd.DataFrame(forecast_data)

def test_basic_functionality():
    """Test base della funzionalità"""
    print("🧪 Test 1: Funzionalità base")
    
    growth_rates = {'Italia': 10, 'Spagna': 8, 'Portogallo': 12, 'Grecia': 15}
    share_closed = 70
    share_disposable = 10
    years = 5
    
    try:
        df = compute_forecast(growth_rates, share_closed, share_disposable, years, False)
        print(f"✅ Test superato: DataFrame creato con {len(df)} righe")
        
        # Verifica struttura
        expected_cols = ['Anno', 'Paese', 'Categoria', 'Ricavi']
        if all(col in df.columns for col in expected_cols):
            print("✅ Struttura DataFrame corretta")
        else:
            print("❌ Struttura DataFrame incorretta")
            
        # Verifica anni
        years_in_df = sorted(df['Anno'].unique())
        expected_years = list(range(2024, 2024 + years + 1))
        if years_in_df == expected_years:
            print("✅ Anni corretti nel DataFrame")
        else:
            print(f"❌ Anni incorretti: attesi {expected_years}, trovati {years_in_df}")
            
    except Exception as e:
        print(f"❌ Test fallito: {e}")

def test_validation():
    """Test validazione input"""
    print("\n🧪 Test 2: Validazione input")
    
    # Test percentuali > 100%
    try:
        growth_rates = {'Italia': 10, 'Spagna': 8, 'Portogallo': 12, 'Grecia': 15}
        df = compute_forecast(growth_rates, 80, 30, 5, False)  # 80 + 30 = 110%
        print("❌ Validazione fallita: dovrebbe rilevare percentuali > 100%")
    except ValueError:
        print("✅ Validazione corretta: percentuali > 100% rilevate")
    
    # Test tassi di crescita estremi
    try:
        growth_rates = {'Italia': 150, 'Spagna': 8, 'Portogallo': 12, 'Grecia': 15}  # 150% troppo alto
        df = compute_forecast(growth_rates, 70, 10, 5, False)
        print("❌ Validazione fallita: dovrebbe rilevare tassi di crescita estremi")
    except ValueError:
        print("✅ Validazione corretta: tassi di crescita estremi rilevati")

def test_calculations():
    """Test calcoli matematici"""
    print("\n🧪 Test 3: Calcoli matematici")
    
    growth_rates = {'Italia': 10, 'Spagna': 0, 'Portogallo': 0, 'Grecia': 0}
    share_closed = 50
    share_disposable = 30
    years = 2
    
    df = compute_forecast(growth_rates, share_closed, share_disposable, years, False)
    
    # Test crescita Italia
    italia_totale = df[(df['Paese'] == 'Italia') & (df['Categoria'] == 'Totale')]
    ricavi_2024 = italia_totale[italia_totale['Anno'] == 2024]['Ricavi'].iloc[0]
    ricavi_2025 = italia_totale[italia_totale['Anno'] == 2025]['Ricavi'].iloc[0]
    
    crescita_attesa = ricavi_2024 * 1.1  # +10%
    if abs(ricavi_2025 - crescita_attesa) < 0.01:
        print("✅ Calcolo crescita corretto")
    else:
        print(f"❌ Calcolo crescita errato: atteso {crescita_attesa}, trovato {ricavi_2025}")
    
    # Test percentuali categorie
    italia_2024 = df[(df['Paese'] == 'Italia') & (df['Anno'] == 2024)]
    chiusi = italia_2024[italia_2024['Categoria'] == 'Chiusi']['Ricavi'].iloc[0]
    monouso = italia_2024[italia_2024['Categoria'] == 'Monouso']['Ricavi'].iloc[0]
    aperti = italia_2024[italia_2024['Categoria'] == 'Aperti']['Ricavi'].iloc[0]
    
    vape_base = base_revenue['Italia']
    chiusi_atteso = vape_base * 0.5  # 50%
    monouso_atteso = vape_base * 0.3  # 30%
    aperti_atteso = vape_base * 0.2  # 20%
    
    if (abs(chiusi - chiusi_atteso) < 0.01 and 
        abs(monouso - monouso_atteso) < 0.01 and 
        abs(aperti - aperti_atteso) < 0.01):
        print("✅ Calcolo percentuali categorie corretto")
    else:
        print("❌ Calcolo percentuali categorie errato")

def test_hnb_functionality():
    """Test funzionalità HnB"""
    print("\n🧪 Test 4: Funzionalità HnB")
    
    growth_rates = {'Italia': 10, 'Spagna': 0, 'Portogallo': 0, 'Grecia': 0}
    
    # Test senza HnB
    df_no_hnb = compute_forecast(growth_rates, 70, 10, 1, False)
    italia_no_hnb = df_no_hnb[(df_no_hnb['Paese'] == 'Italia') & (df_no_hnb['Categoria'] == 'Totale') & (df_no_hnb['Anno'] == 2024)]['Ricavi'].iloc[0]
    
    # Test con HnB
    df_with_hnb = compute_forecast(growth_rates, 70, 10, 1, True)
    italia_with_hnb = df_with_hnb[(df_with_hnb['Paese'] == 'Italia') & (df_with_hnb['Categoria'] == 'Totale') & (df_with_hnb['Anno'] == 2024)]['Ricavi'].iloc[0]
    
    differenza_attesa = base_hnb['Italia']
    differenza_reale = italia_with_hnb - italia_no_hnb
    
    if abs(differenza_reale - differenza_attesa) < 0.01:
        print("✅ Funzionalità HnB corretta")
    else:
        print(f"❌ Funzionalità HnB errata: differenza attesa {differenza_attesa}, trovata {differenza_reale}")

if __name__ == "__main__":
    print("🚀 Avvio test dashboard E-Cig Sud Europa\n")
    
    test_basic_functionality()
    test_validation()
    test_calculations()
    test_hnb_functionality()
    
    print("\n✅ Test completati!")
