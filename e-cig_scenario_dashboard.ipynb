{"cells": [{"cell_type": "markdown", "id": "fe112dde", "metadata": {}, "source": ["\n", "# Dashboard di Scenario per il Mercato E-Cig Sud Europa\n", "\n", "Questo notebook interattivo permette di simulare diversi scenari di crescita per il mercato delle sigarette elettroniche e dei prodotti a tabacco riscaldato in Italia, Spagna, Portogallo e Grecia. Utilizza **ipywidgets** per modificare i parametri e **Plotly** per visualizzare i risultati.\n", "\n", "## Come usare il simulatore\n", "\n", "1. <PERSON><PERSON> i tassi di crescita annuale per ogni paese tramite gli slider.\n", "2. Imposta la distribuzione delle categorie (sistemi chiusi, sistemi aperti e dispositivi monouso). I valori devono sommare al 100%.\n", "3. <PERSON><PERSON><PERSON> l'orizzonte temporale (numero di anni da simulare).\n", "4. Fai clic su **Aggiorna** per generare il grafico interattivo con i nuovi parametri.\n", "\n", "I dati di base utilizzati provengono da ricerche di mercato recenti e sono riportati nel report principale.\n"]}, {"cell_type": "code", "execution_count": null, "id": "6589cea7", "metadata": {}, "outputs": [], "source": ["\n", "import pandas as pd\n", "import numpy as np\n", "import plotly.graph_objects as go\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "\n", "# Dati base (milioni USD) per il 2024\n", "base_revenue = {\n", "    'Italia': 726.33,   # mercato vape 2024 (Cognitive Market Research)\n", "    'Spagna': 692.54,   # mercato vape 2024 (Cognitive Market Research)\n", "    'Portogallo': 177.36, # mercato vape 2024 (Cognitive Market Research)\n", "    'Grecia': 101.35    # mercato vape 2024 (Cognitive Market Research)\n", "}\n", "\n", "# Dati base HnB (Heat-not-burn) 2024 (solo per Italia – altri paesi dati non disponibili)\n", "base_hnb = {\n", "    'Italia': 4500,  # milioni USD (TobaccoIntelligence 2024)\n", "    'Spagna': 0,\n", "    'Portogallo': 0,\n", "    'Grecia': 0\n", "}\n", "\n", "# Funzione per calcolare i ricavi futuri\n", "\n", "def compute_forecast(growth_rates, share_closed, share_disposable, years, include_hnb=False):\n", "    # growth_rates: dizionario con tassi di crescita annuale (%).\n", "    # share_closed: percentuale di sistemi chiusi.\n", "    # share_disposable: percentuale di dispositivi monouso.\n", "    # years: numero di anni per la simulazione.\n", "    # include_hnb: se True, include i ricavi HnB per l'Italia.\n", "    share_open = 100 - share_closed - share_disposable\n", "    forecast_data = []\n", "    for country, base in base_revenue.items():\n", "        revenue = base\n", "        hnb_rev = base_hnb.get(country, 0) if include_hnb else 0\n", "        for year in range(years + 1):\n", "            total_rev = revenue + hnb_rev\n", "            forecast_data.append({\n", "                'Anno': 2024 + year,\n", "                'Paese': country,\n", "                'Categoria': 'Totale',\n", "                'Ricavi': total_rev\n", "            })\n", "            # ricavi per categoria (solo vape)\n", "            forecast_data.append({\n", "                'Anno': 2024 + year,\n", "                'Paese': country,\n", "                'Categoria': '<PERSON><PERSON><PERSON>',\n", "                'Ricavi': revenue * (share_closed/100)\n", "            })\n", "            forecast_data.append({\n", "                'Anno': 2024 + year,\n", "                'Paese': country,\n", "                'Categoria': 'Aperti',\n", "                'Ricavi': revenue * (share_open/100)\n", "            })\n", "            forecast_data.append({\n", "                'Anno': 2024 + year,\n", "                'Paese': country,\n", "                'Categoria': '<PERSON><PERSON><PERSON>',\n", "                'Ricavi': revenue * (share_disposable/100)\n", "            })\n", "            rate = growth_rates.get(country, 0) / 100\n", "            revenue = revenue * (1 + rate)\n", "            hnb_rev = hnb_rev * (1 + rate) if include_hnb else 0\n", "    return pd.DataFrame(forecast_data)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "13a4e7fe", "metadata": {}, "outputs": [], "source": ["\n", "# Creazione degli slider per i tassi di crescita\n", "growth_sliders = {}\n", "for country in base_revenue.keys():\n", "    growth_sliders[country] = widgets.FloatSlider(\n", "        value=10.0,\n", "        min=-5.0,\n", "        max=30.0,\n", "        step=0.5,\n", "        description=f\"Crescita {country} (%)\",\n", "        continuous_update=False\n", "    )\n", "\n", "# Slider per la percentuale di dispositivi monouso\n", "disposable_slider = widgets.FloatSlider(\n", "    value=10.0,\n", "    min=0.0,\n", "    max=90.0,\n", "    step=1.0,\n", "    description='<PERSON><PERSON><PERSON> (%)',\n", "    continuous_update=False\n", ")\n", "\n", "# Slider per la percentuale di sistemi chiusi\n", "closed_slider = widgets.FloatSlider(\n", "    value=70.0,\n", "    min=0.0,\n", "    max=100.0,\n", "    step=1.0,\n", "    description='<PERSON><PERSON><PERSON> (%)',\n", "    continuous_update=False\n", ")\n", "\n", "# Slider per la durata dell'orizzonte temporale\n", "years_slider = widgets.IntSlider(\n", "    value=5,\n", "    min=1,\n", "    max=10,\n", "    step=1,\n", "    description='Orizzonte (anni)',\n", "    continuous_update=False\n", ")\n", "\n", "# Checkbox per includere la categoria Heat-not-burn (solo Italia)\n", "hnb_checkbox = widgets.Checkbox(\n", "    value=False,\n", "    description='Includi HnB (Italia)'\n", ")\n", "\n", "# Bottone per aggiornare il grafico\n", "update_button = widgets.Button(description='Aggiorna', button_style='info')\n", "\n", "# Area di output\n", "output = widgets.Output()\n", "\n", "# Funzione di callback da eseguire al click del bottone\n", "def update_plot(button=None):\n", "    with output:\n", "        output.clear_output()\n", "        # Costruisci dizionario dei tassi di crescita\n", "        growth_rates = {country: growth_sliders[country].value for country in base_revenue.keys()}\n", "        share_closed = closed_slider.value\n", "        share_disposable = disposable_slider.value\n", "        years = years_slider.value\n", "        include_hnb = hnb_checkbox.value\n", "        if share_closed + share_disposable > 100:\n", "            print(\"Errore: la somma di '<PERSON>usi' e 'Monouso' non può superare 100%.\")\n", "            return\n", "        df = compute_forecast(growth_rates, share_closed, share_disposable, years, include_hnb)\n", "        fig = go.Figure()\n", "        for country in base_revenue.keys():\n", "            df_country = df[(df['Paese'] == country) & (df['Categoria'] == 'Totale')]\n", "            fig.add_trace(go.<PERSON>er(x=df_country['Anno'], y=df_country['Ricavi'], mode='lines+markers', name=country))\n", "        fig.update_layout(\n", "            title=\"Proiezioni di Ricavi Totali per Paese\",\n", "            xaxis_title=\"Anno\",\n", "            yaxis_title=\"<PERSON>vi (milioni USD)\",\n", "            legend_title=\"Paese\",\n", "            template='plotly_white'\n", "        )\n", "        fig.show()\n", "        # <PERSON><PERSON> riassuntiva per il primo e l'ultimo anno\n", "        first_year = 2024\n", "        last_year = 2024 + years\n", "        summary = df[df['Anno'].isin([first_year, last_year])].pivot_table(index=['Paese', 'Categoria'], columns='Anno', values='Ricavi').reset_index()\n", "        display(summary)\n", "\n", "# Collega la funzione al bottone\n", "update_button.on_click(update_plot)\n", "\n", "# Visualizza i widget\n", "ui = widgets.VBox([\n", "    widgets.HBox(list(growth_sliders.values())),\n", "    closed_slider,\n", "    disposable_slider,\n", "    years_slider,\n", "    hnb_checkbox,\n", "    update_button,\n", "    output\n", "])\n", "\n", "display(ui)\n"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}